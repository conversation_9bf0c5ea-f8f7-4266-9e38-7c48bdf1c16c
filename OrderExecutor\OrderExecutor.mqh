#include "../mql4-lib/Trade/OrderManager.mqh"
#include "../mql4-lib/Collection/Vector.mqh"

//+------------------------------------------------------------------+
//| 訂單執行請求類別                                                 |
//+------------------------------------------------------------------+

class OrderRequest{
private:
    int         m_op;
    double      m_lots;
    int         m_stoploss;
    int         m_takeprofit;
    string      m_comment;
public:
    OrderRequest(int op,double lots,int stoploss,int takeprofit,string comment)
    : m_op(op), m_lots(lots), m_stoploss(stoploss), m_takeprofit(takeprofit), m_comment(comment) {}
    int         GetOp() const { return m_op; }
    double      GetLots() const { return m_lots; }
    int         GetStopLoss() const { return m_stoploss; }
    int         GetTakeProfit() const { return m_takeprofit; }
    string      GetComment() const { return m_comment; }
};

class BatchOrderRequest{
private:
    Vector<OrderRequest*> m_requests;
public:
    void            AddRequest(OrderRequest* request) { m_requests.add(request); }
    int             GetRequestCount() const { return m_requests.size(); }
    OrderRequest*   GetRequest(int index) const { return m_requests[index]; }
};

class ModifyRequest{
private:
    int         m_ticket;
    int         m_stoploss;
    int         m_takeprofit;
public:
    ModifyRequest(int ticket,int stoploss,int takeprofit)
    : m_ticket(ticket), m_stoploss(stoploss), m_takeprofit(takeprofit) {}
    int         GetTicket() const { return m_ticket; }
    int         GetStopLoss() const { return m_stoploss; }
    int         GetTakeProfit() const { return m_takeprofit; }
};

class BatchModifyRequest{
private:
    Vector<ModifyRequest*> m_requests;
public:
    void            AddRequest(ModifyRequest* request) { m_requests.add(request); }
    int             GetRequestCount() const { return m_requests.size(); }
    ModifyRequest*  GetRequest(int index) const { return m_requests[index]; }
};

//+------------------------------------------------------------------+
//| 關閉訂單請求類別                                                 |
//+------------------------------------------------------------------+
class CloseRequest{
private:
    int         m_ticket;
    double      m_lots;        // 0 表示完全關閉
public:
    CloseRequest(int ticket, double lots = 0.0)
    : m_ticket(ticket), m_lots(lots) {}
    int         GetTicket() const { return m_ticket; }
    double      GetLots() const { return m_lots; }
    bool        IsPartialClose() const { return m_lots > 0.0; }
};

//+------------------------------------------------------------------+
//| 批次關閉訂單請求類別                                             |
//+------------------------------------------------------------------+
class BatchCloseRequest{
private:
    Vector<CloseRequest*> m_requests;
public:
    void            AddRequest(CloseRequest* request) { m_requests.add(request); }
    int             GetRequestCount() const { return m_requests.size(); }
    CloseRequest*   GetRequest(int index) const { return m_requests[index]; }
};

//+------------------------------------------------------------------+
//| OrderExecutor 類別 - 訂單執行器                                  |
//| 提供單一和批次訂單操作功能                                       |
//+------------------------------------------------------------------+
class OrderExecutor : public OrderManager
{
public:
    //--- 構造函數
    OrderExecutor(string symbol) : OrderManager(symbol) {}

    //--- 單一訂單操作方法
    int             Market(OrderRequest* request);
    bool            Modify(ModifyRequest* request);
    bool            Close(CloseRequest* request);

    //--- 批次訂單操作方法
    Vector<int>*    BatchMarket(BatchOrderRequest* request);
    Vector<bool>*   BatchModify(BatchModifyRequest* request);
    Vector<bool>*   BatchClose(BatchCloseRequest* request);


};

//+------------------------------------------------------------------+
//| OrderExecutor 方法實現                                          |
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| 執行市價單請求                                                   |
//+------------------------------------------------------------------+
int OrderExecutor::Market(OrderRequest* request)
{
    if(request == NULL)
    {
        Print("錯誤 [OrderExecutor::Market]: OrderRequest 指針為空");
        return -1;
    }

    // 驗證操作類型
    int op = request.GetOp();
    if(op != OP_BUY && op != OP_SELL)
    {
        Print("錯誤 [OrderExecutor::Market]: 無效的操作類型 ", op, "，只支援 OP_BUY(0) 和 OP_SELL(1)");
        return -1;
    }

    // 驗證手數
    double lots = request.GetLots();
    if(lots <= 0.0)
    {
        Print("錯誤 [OrderExecutor::Market]: 無效的手數 ", lots, "，必須大於 0");
        return -1;
    }

    // 驗證止損和止盈（負值檢查）
    int stoploss = request.GetStopLoss();
    int takeprofit = request.GetTakeProfit();

    if(stoploss < 0)
    {
        Print("錯誤 [OrderExecutor::Market]: 無效的止損點數 ", stoploss, "，不能為負值");
        return -1;
    }

    if(takeprofit < 0)
    {
        Print("錯誤 [OrderExecutor::Market]: 無效的止盈點數 ", takeprofit, "，不能為負值");
        return -1;
    }

    return market(op, lots, stoploss, takeprofit, request.GetComment());
}



//+------------------------------------------------------------------+
//| 修改訂單請求                                                     |
//+------------------------------------------------------------------+
bool OrderExecutor::Modify(ModifyRequest* request)
{
    if(request == NULL)
    {
        Print("錯誤 [OrderExecutor::Modify]: ModifyRequest 指針為空");
        return false;
    }

    // 驗證票號
    int ticket = request.GetTicket();
    if(ticket <= 0)
    {
        Print("錯誤 [OrderExecutor::Modify]: 無效的訂單票號 ", ticket, "，必須大於 0");
        return false;
    }

    // 驗證止損和止盈
    int stoploss = request.GetStopLoss();
    int takeprofit = request.GetTakeProfit();

    if(stoploss < 0)
    {
        Print("錯誤 [OrderExecutor::Modify]: 無效的止損點數 ", stoploss, "，不能為負值");
        return false;
    }

    if(takeprofit < 0)
    {
        Print("錯誤 [OrderExecutor::Modify]: 無效的止盈點數 ", takeprofit, "，不能為負值");
        return false;
    }

    // 檢查是否至少有一個參數需要修改
    if(stoploss == 0 && takeprofit == 0)
    {
        Print("警告 [OrderExecutor::Modify]: 止損和止盈都為 0，沒有需要修改的內容");
    }

    return modify(ticket, stoploss, takeprofit);
}

//+------------------------------------------------------------------+
//| 關閉訂單請求                                                     |
//+------------------------------------------------------------------+
bool OrderExecutor::Close(CloseRequest* request)
{
    if(request == NULL)
    {
        Print("錯誤 [OrderExecutor::Close]: CloseRequest 指針為空");
        return false;
    }

    // 驗證票號
    int ticket = request.GetTicket();
    if(ticket <= 0)
    {
        Print("錯誤 [OrderExecutor::Close]: 無效的訂單票號 ", ticket, "，必須大於 0");
        return false;
    }

    // 驗證手數（如果是部分關閉）
    double lots = request.GetLots();
    if(request.IsPartialClose())
    {
        if(lots <= 0.0)
        {
            Print("錯誤 [OrderExecutor::Close]: 部分關閉的手數 ", lots, " 無效，必須大於 0");
            return false;
        }

        Print("資訊 [OrderExecutor::Close]: 執行部分關閉，票號 ", ticket, "，手數 ", lots);
        return close(ticket, lots);
    }
    else
    {
        Print("資訊 [OrderExecutor::Close]: 執行完全關閉，票號 ", ticket);
        return close(ticket);
    }
}



//+------------------------------------------------------------------+
//| 批次市價單執行                                                   |
//+------------------------------------------------------------------+
Vector<int>* OrderExecutor::BatchMarket(BatchOrderRequest* request)
{
    if(request == NULL)
    {
        Print("錯誤 [OrderExecutor::BatchMarket]: BatchOrderRequest 指針為空");
        return NULL;
    }

    int count = request.GetRequestCount();
    if(count <= 0)
    {
        Print("錯誤 [OrderExecutor::BatchMarket]: 批次請求為空，沒有訂單需要處理");
        return NULL;
    }

    Print("資訊 [OrderExecutor::BatchMarket]: 開始處理批次市價單，共 ", count, " 個訂單");

    Vector<int>* results = new Vector<int>(false); // 不擁有元素
    int successCount = 0;
    int failureCount = 0;

    for(int i = 0; i < count; i++)
    {
        OrderRequest* orderReq = request.GetRequest(i);
        if(orderReq == NULL)
        {
            Print("錯誤 [OrderExecutor::BatchMarket]: 第 ", i+1, " 個 OrderRequest 為空，跳過");
            results.add(-1);
            failureCount++;
            continue;
        }

        int ticket = Market(orderReq);
        results.add(ticket);

        if(ticket > 0)
        {
            successCount++;
            Print("成功 [OrderExecutor::BatchMarket]: 第 ", i+1, " 個訂單執行成功，票號 ", ticket);
        }
        else
        {
            failureCount++;
            Print("失敗 [OrderExecutor::BatchMarket]: 第 ", i+1, " 個訂單執行失敗");
        }
    }

    Print("資訊 [OrderExecutor::BatchMarket]: 批次處理完成，成功 ", successCount, " 個，失敗 ", failureCount, " 個");
    return results;
}

//+------------------------------------------------------------------+
//| 批次訂單修改                                                     |
//+------------------------------------------------------------------+
Vector<bool>* OrderExecutor::BatchModify(BatchModifyRequest* request)
{
    if(request == NULL)
    {
        Print("錯誤 [OrderExecutor::BatchModify]: BatchModifyRequest 指針為空");
        return NULL;
    }

    int count = request.GetRequestCount();
    if(count <= 0)
    {
        Print("錯誤 [OrderExecutor::BatchModify]: 批次請求為空，沒有修改需要處理");
        return NULL;
    }

    Print("資訊 [OrderExecutor::BatchModify]: 開始處理批次修改，共 ", count, " 個訂單");

    Vector<bool>* results = new Vector<bool>(false); // 不擁有元素
    int successCount = 0;
    int failureCount = 0;

    for(int i = 0; i < count; i++)
    {
        ModifyRequest* modifyReq = request.GetRequest(i);
        if(modifyReq == NULL)
        {
            Print("錯誤 [OrderExecutor::BatchModify]: 第 ", i+1, " 個 ModifyRequest 為空，跳過");
            results.add(false);
            failureCount++;
            continue;
        }

        bool success = Modify(modifyReq);
        results.add(success);

        if(success)
        {
            successCount++;
            Print("成功 [OrderExecutor::BatchModify]: 第 ", i+1, " 個訂單修改成功，票號 ", modifyReq.GetTicket());
        }
        else
        {
            failureCount++;
            Print("失敗 [OrderExecutor::BatchModify]: 第 ", i+1, " 個訂單修改失敗，票號 ", modifyReq.GetTicket());
        }
    }

    Print("資訊 [OrderExecutor::BatchModify]: 批次處理完成，成功 ", successCount, " 個，失敗 ", failureCount, " 個");
    return results;
}

//+------------------------------------------------------------------+
//| 批次訂單關閉                                                     |
//+------------------------------------------------------------------+
Vector<bool>* OrderExecutor::BatchClose(BatchCloseRequest* request)
{
    if(request == NULL)
    {
        Print("錯誤 [OrderExecutor::BatchClose]: BatchCloseRequest 指針為空");
        return NULL;
    }

    int count = request.GetRequestCount();
    if(count <= 0)
    {
        Print("錯誤 [OrderExecutor::BatchClose]: 批次請求為空，沒有關閉需要處理");
        return NULL;
    }

    Print("資訊 [OrderExecutor::BatchClose]: 開始處理批次關閉，共 ", count, " 個訂單");

    Vector<bool>* results = new Vector<bool>(false); // 不擁有元素
    int successCount = 0;
    int failureCount = 0;

    for(int i = 0; i < count; i++)
    {
        CloseRequest* closeReq = request.GetRequest(i);
        if(closeReq == NULL)
        {
            Print("錯誤 [OrderExecutor::BatchClose]: 第 ", i+1, " 個 CloseRequest 為空，跳過");
            results.add(false);
            failureCount++;
            continue;
        }

        bool success = Close(closeReq);
        results.add(success);

        if(success)
        {
            successCount++;
            string closeType = closeReq.IsPartialClose() ? "部分關閉" : "完全關閉";
            Print("成功 [OrderExecutor::BatchClose]: 第 ", i+1, " 個訂單", closeType, "成功，票號 ", closeReq.GetTicket());
        }
        else
        {
            failureCount++;
            Print("失敗 [OrderExecutor::BatchClose]: 第 ", i+1, " 個訂單關閉失敗，票號 ", closeReq.GetTicket());
        }
    }

    Print("資訊 [OrderExecutor::BatchClose]: 批次處理完成，成功 ", successCount, " 個，失敗 ", failureCount, " 個");
    return results;
}