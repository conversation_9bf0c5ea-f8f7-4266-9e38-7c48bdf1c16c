#include "../mql4-lib/Trade/OrderManager.mqh"
#include "../mql4-lib/Collection/Vector.mqh"

//+------------------------------------------------------------------+
//| 訂單執行請求類別                                                 |
//+------------------------------------------------------------------+

class OrderRequest{
private:
    int         m_op;
    double      m_lots;
    int         m_stoploss;
    int         m_takeprofit;
    string      m_comment;
public:
    OrderRequest(int op,double lots,int stoploss,int takeprofit,string comment)
    : m_op(op), m_lots(lots), m_stoploss(stoploss), m_takeprofit(takeprofit), m_comment(comment) {}
    int         GetOp() const { return m_op; }
    double      GetLots() const { return m_lots; }
    int         GetStopLoss() const { return m_stoploss; }
    int         GetTakeProfit() const { return m_takeprofit; }
    string      GetComment() const { return m_comment; }
};

class BatchOrderRequest{
private:
    Vector<OrderRequest*> m_requests;
public:
    void            AddRequest(OrderRequest* request) { m_requests.add(request); }
    int             GetRequestCount() const { return m_requests.size(); }
    OrderRequest*   GetRequest(int index) const { return m_requests[index]; }
};

class ModifyRequest{
private:
    int         m_ticket;
    int         m_stoploss;
    int         m_takeprofit;
public:
    ModifyRequest(int ticket,int stoploss,int takeprofit)
    : m_ticket(ticket), m_stoploss(stoploss), m_takeprofit(takeprofit) {}
    int         GetTicket() const { return m_ticket; }
    int         GetStopLoss() const { return m_stoploss; }
    int         GetTakeProfit() const { return m_takeprofit; }
};

class BatchModifyRequest{
private:
    Vector<ModifyRequest*> m_requests;
public:
    void            AddRequest(ModifyRequest* request) { m_requests.add(request); }
    int             GetRequestCount() const { return m_requests.size(); }
    ModifyRequest*  GetRequest(int index) const { return m_requests[index]; }
};

//+------------------------------------------------------------------+
//| 關閉訂單請求類別                                                 |
//+------------------------------------------------------------------+
class CloseRequest{
private:
    int         m_ticket;
    double      m_lots;        // 0 表示完全關閉
public:
    CloseRequest(int ticket, double lots = 0.0)
    : m_ticket(ticket), m_lots(lots) {}
    int         GetTicket() const { return m_ticket; }
    double      GetLots() const { return m_lots; }
    bool        IsPartialClose() const { return m_lots > 0.0; }
};

//+------------------------------------------------------------------+
//| 批次關閉訂單請求類別                                             |
//+------------------------------------------------------------------+
class BatchCloseRequest{
private:
    Vector<CloseRequest*> m_requests;
public:
    void            AddRequest(CloseRequest* request) { m_requests.add(request); }
    int             GetRequestCount() const { return m_requests.size(); }
    CloseRequest*   GetRequest(int index) const { return m_requests[index]; }
};

//+------------------------------------------------------------------+
//| OrderExecutor 類別 - 訂單執行器                                  |
//| 提供單一和批次訂單操作功能                                       |
//+------------------------------------------------------------------+
class OrderExecutor : public OrderManager
{
public:
    //--- 構造函數
    OrderExecutor(string symbol) : OrderManager(symbol) {}

    //--- 單一訂單操作方法
    int             Market(OrderRequest* request);
    bool            Modify(ModifyRequest* request);
    bool            Close(CloseRequest* request);

    //--- 批次訂單操作方法
    Vector<int>*    BatchMarket(BatchOrderRequest* request);
    Vector<bool>*   BatchModify(BatchModifyRequest* request);
    Vector<bool>*   BatchClose(BatchCloseRequest* request);

    //--- 便利方法
    int             Market(int op, double lots, int stoploss, int takeprofit, string comment = NULL);
    bool            Close(int ticket, double lots = 0.0);
};

//+------------------------------------------------------------------+
//| OrderExecutor 方法實現                                          |
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| 執行市價單請求                                                   |
//+------------------------------------------------------------------+
int OrderExecutor::Market(OrderRequest* request)
{
    if(request == NULL) return -1;

    return market(request.GetOp(),
                  request.GetLots(),
                  request.GetStopLoss(),
                  request.GetTakeProfit(),
                  request.GetComment());
}

//+------------------------------------------------------------------+
//| 便利方法：直接執行市價單                                         |
//+------------------------------------------------------------------+
int OrderExecutor::Market(int op, double lots, int stoploss, int takeprofit, string comment = NULL)
{
    return market(op, lots, stoploss, takeprofit, comment);
}

//+------------------------------------------------------------------+
//| 修改訂單請求                                                     |
//+------------------------------------------------------------------+
bool OrderExecutor::Modify(ModifyRequest* request)
{
    if(request == NULL) return false;

    return modify(request.GetTicket(),
                  request.GetStopLoss(),
                  request.GetTakeProfit());
}

//+------------------------------------------------------------------+
//| 關閉訂單請求                                                     |
//+------------------------------------------------------------------+
bool OrderExecutor::Close(CloseRequest* request)
{
    if(request == NULL) return false;

    if(request.IsPartialClose())
    {
        return close(request.GetTicket(), request.GetLots());
    }
    else
    {
        return close(request.GetTicket());
    }
}

//+------------------------------------------------------------------+
//| 便利方法：直接關閉訂單                                           |
//+------------------------------------------------------------------+
bool OrderExecutor::Close(int ticket, double lots = 0.0)
{
    if(lots > 0.0)
    {
        return close(ticket, lots);
    }
    else
    {
        return close(ticket);
    }
}

//+------------------------------------------------------------------+
//| 批次市價單執行                                                   |
//+------------------------------------------------------------------+
Vector<int>* OrderExecutor::BatchMarket(BatchOrderRequest* request)
{
    if(request == NULL) return NULL;

    Vector<int>* results = new Vector<int>(false); // 不擁有元素

    int count = request.GetRequestCount();
    for(int i = 0; i < count; i++)
    {
        OrderRequest* orderReq = request.GetRequest(i);
        int ticket = Market(orderReq);
        results.add(ticket);
    }

    return results;
}

//+------------------------------------------------------------------+
//| 批次訂單修改                                                     |
//+------------------------------------------------------------------+
Vector<bool>* OrderExecutor::BatchModify(BatchModifyRequest* request)
{
    if(request == NULL) return NULL;

    Vector<bool>* results = new Vector<bool>(false); // 不擁有元素

    int count = request.GetRequestCount();
    for(int i = 0; i < count; i++)
    {
        ModifyRequest* modifyReq = request.GetRequest(i);
        bool success = Modify(modifyReq);
        results.add(success);
    }

    return results;
}

//+------------------------------------------------------------------+
//| 批次訂單關閉                                                     |
//+------------------------------------------------------------------+
Vector<bool>* OrderExecutor::BatchClose(BatchCloseRequest* request)
{
    if(request == NULL) return NULL;

    Vector<bool>* results = new Vector<bool>(false); // 不擁有元素

    int count = request.GetRequestCount();
    for(int i = 0; i < count; i++)
    {
        CloseRequest* closeReq = request.GetRequest(i);
        bool success = Close(closeReq);
        results.add(success);
    }

    return results;
}