#include "../mql4-lib/Trade/OrderManager.mqh"
#include "../mql4-lib/Collection/Vector.mqh"

class OrderRequest{
private:
    int         m_op;
    double      m_lots;
    int         m_stoploss;
    int         m_takeprofit;
    string      m_comment;
public:
    OrderRequest(int op,double lots,int stoploss,int takeprofit,string comment)
    : m_op(op), m_lots(lots), m_stoploss(stoploss), m_takeprofit(takeprofit), m_comment(comment) {}
    int         GetOp() const { return m_op; }
    double      GetLots() const { return m_lots; }
    int         GetStopLoss() const { return m_stoploss; }
    int         GetTakeProfit() const { return m_takeprofit; }
    string      GetComment() const { return m_comment; }
};

class BatchOrderRequest{
private:
    Vector<OrderRequest*> m_requests;
public:
    void            AddRequest(OrderRequest* request) { m_requests.add(request); }
    int             GetRequestCount() const { return m_requests.size(); }
    OrderRequest*   GetRequest(int index) const { return m_requests[index]; }
};

class ModifyRequest{
private:
    int         m_ticket;
    int         m_stoploss;
    int         m_takeprofit;
public:
    ModifyRequest(int ticket,int stoploss,int takeprofit)
    : m_ticket(ticket), m_stoploss(stoploss), m_takeprofit(takeprofit) {}
    int         GetTicket() const { return m_ticket; }
    int         GetStopLoss() const { return m_stoploss; }
    int         GetTakeProfit() const { return m_takeprofit; }
};

class BatchModifyRequest{
private:
    Vector<ModifyRequest*> m_requests;
public:
    void            AddRequest(ModifyRequest* request) { m_requests.add(request); }
    int             GetRequestCount() const { return m_requests.size(); }
    ModifyRequest*  GetRequest(int index) const { return m_requests[index]; }
};

class OrderExecutor : public OrderManager
{
    //int Market(OrderRequest* request);
    //bool Modify(ModifyRequest* request);
    //bool Close(int ticket);

    //Collection* BatchMarket(BatchOrderRequest* request);
    //Collection* BatchModify(BatchModifyRequest* request);
    //Collection* BatchClose(BatchCloseRequest* request);
};