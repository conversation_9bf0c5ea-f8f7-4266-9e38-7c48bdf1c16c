//+------------------------------------------------------------------+
//| example_usage.mq4                                               |
//| OrderExecutor 使用範例                                          |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard Framework"
#property version   "1.00"
#property strict

#include "OrderExecutor.mqh"

// 全域變數
OrderExecutor* g_executor = NULL;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    Print("=== OrderExecutor 使用範例 ===");

    // 創建 OrderExecutor 實例
    g_executor = new OrderExecutor(Symbol());

    if(g_executor == NULL)
    {
        Print("錯誤：無法創建 OrderExecutor 實例");
        return INIT_FAILED;
    }

    // 設置 OrderManager 參數
    g_executor.setMagic(12345);
    g_executor.setSlippage(3);
    g_executor.setRetry(3);

    Print("✓ OrderExecutor 初始化成功");
    Print("  交易品種: ", Symbol());
    Print("  魔術數字: ", g_executor.getMagic());
    Print("  滑點: ", g_executor.getSlippage());

    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    // 清理資源
    if(g_executor != NULL)
    {
        delete g_executor;
        g_executor = NULL;
    }

    Print("OrderExecutor 資源已清理");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    // 這裡可以添加交易邏輯
    // 範例：每100個tick執行一次示範操作
    static int tickCount = 0;
    tickCount++;

    if(tickCount >= 100)
    {
        tickCount = 0;
        DemonstrateOrderOperations();
    }
}

//+------------------------------------------------------------------+
//| 示範訂單操作                                                     |
//+------------------------------------------------------------------+
void DemonstrateOrderOperations()
{
    if(g_executor == NULL) return;

    Print("--- 示範訂單操作 ---");

    // 示範1：使用便利方法執行市價單
    DemonstrateSimpleMarketOrder();

    // 示範2：使用 OrderRequest 執行市價單
    DemonstrateOrderRequestUsage();

    // 示範3：批次訂單操作
    DemonstrateBatchOperations();
}

//+------------------------------------------------------------------+
//| 示範使用 OrderRequest 執行市價單                                 |
//+------------------------------------------------------------------+
void DemonstrateSimpleMarketOrder()
{
    Print("示範1：使用 OrderRequest 執行市價單");

    // 創建訂單請求
    OrderRequest* request = new OrderRequest(OP_BUY, 0.1, 50, 100, "Demo Buy Order");

    if(request != NULL)
    {
        Print("  創建 OrderRequest:");
        Print("    操作: ", request.GetOp() == OP_BUY ? "買入" : "賣出");
        Print("    手數: ", request.GetLots());
        Print("    止損: ", request.GetStopLoss(), " 點");
        Print("    止盈: ", request.GetTakeProfit(), " 點");
        Print("    註釋: ", request.GetComment());

        // 執行訂單（示範代碼）
        // int ticket = g_executor.Market(request);
        Print("  呼叫: g_executor.Market(request)");
        Print("  說明: 使用 OrderRequest 物件執行市價單");

        delete request;
    }
}

//+------------------------------------------------------------------+
//| 示範訂單修改和關閉                                               |
//+------------------------------------------------------------------+
void DemonstrateOrderRequestUsage()
{
    Print("示範2：訂單修改和關閉操作");

    // 假設已有訂單票號
    int demoTicket = 12345;

    // 示範修改訂單
    ModifyRequest* modifyReq = new ModifyRequest(demoTicket, 30, 90);
    if(modifyReq != NULL)
    {
        Print("  創建 ModifyRequest:");
        Print("    票號: ", modifyReq.GetTicket());
        Print("    新止損: ", modifyReq.GetStopLoss(), " 點");
        Print("    新止盈: ", modifyReq.GetTakeProfit(), " 點");

        // 執行修改（示範代碼）
        // bool success = g_executor.Modify(modifyReq);
        Print("  呼叫: g_executor.Modify(modifyReq)");

        delete modifyReq;
    }

    // 示範關閉訂單
    CloseRequest* closeReq = new CloseRequest(demoTicket, 0.05); // 部分關閉
    if(closeReq != NULL)
    {
        Print("  創建 CloseRequest:");
        Print("    票號: ", closeReq.GetTicket());
        Print("    關閉手數: ", closeReq.GetLots());
        Print("    是否部分關閉: ", closeReq.IsPartialClose() ? "是" : "否");

        // 執行關閉（示範代碼）
        // bool success = g_executor.Close(closeReq);
        Print("  呼叫: g_executor.Close(closeReq)");

        delete closeReq;
    }
}

//+------------------------------------------------------------------+
//| 示範批次操作                                                     |
//+------------------------------------------------------------------+
void DemonstrateBatchOperations()
{
    Print("示範3：批次訂單操作");

    // 創建批次訂單請求
    BatchOrderRequest* batchRequest = new BatchOrderRequest();

    if(batchRequest != NULL)
    {
        // 添加多個訂單
        batchRequest.AddRequest(new OrderRequest(OP_BUY, 0.1, 30, 60, "Batch Buy 1"));
        batchRequest.AddRequest(new OrderRequest(OP_BUY, 0.1, 35, 70, "Batch Buy 2"));
        batchRequest.AddRequest(new OrderRequest(OP_SELL, 0.1, 40, 80, "Batch Sell 1"));

        Print("  創建批次訂單請求，包含 ", batchRequest.GetRequestCount(), " 個訂單");

        // 執行批次訂單（示範代碼）
        // Vector<int>* results = g_executor.BatchMarket(batchRequest);
        Print("  呼叫: g_executor.BatchMarket(batchRequest)");
        Print("  說明: 批次執行多個市價單，返回票號向量");

        // 清理資源
        for(int i = 0; i < batchRequest.GetRequestCount(); i++)
        {
            delete batchRequest.GetRequest(i);
        }
        delete batchRequest;
    }

    // 示範批次修改操作
    DemonstrateBatchModify();

    // 示範批次關閉操作
    DemonstrateBatchClose();
}

//+------------------------------------------------------------------+
//| 示範批次修改                                                     |
//+------------------------------------------------------------------+
void DemonstrateBatchModify()
{
    Print("  批次修改示範:");

    BatchModifyRequest* batchModify = new BatchModifyRequest();

    if(batchModify != NULL)
    {
        // 添加修改請求（假設票號）
        batchModify.AddRequest(new ModifyRequest(12345, 25, 75));
        batchModify.AddRequest(new ModifyRequest(12346, 30, 80));

        Print("    創建批次修改請求，包含 ", batchModify.GetRequestCount(), " 個修改");
        Print("    呼叫: g_executor.BatchModify(batchModify)");

        // 清理資源
        for(int i = 0; i < batchModify.GetRequestCount(); i++)
        {
            delete batchModify.GetRequest(i);
        }
        delete batchModify;
    }
}

//+------------------------------------------------------------------+
//| 示範批次關閉                                                     |
//+------------------------------------------------------------------+
void DemonstrateBatchClose()
{
    Print("  批次關閉示範:");

    BatchCloseRequest* batchClose = new BatchCloseRequest();

    if(batchClose != NULL)
    {
        // 添加關閉請求（假設票號）
        batchClose.AddRequest(new CloseRequest(12345, 0.0));   // 完全關閉
        batchClose.AddRequest(new CloseRequest(12346, 0.05));  // 部分關閉

        Print("    創建批次關閉請求，包含 ", batchClose.GetRequestCount(), " 個關閉操作");
        Print("    呼叫: g_executor.BatchClose(batchClose)");

        // 清理資源
        for(int i = 0; i < batchClose.GetRequestCount(); i++)
        {
            delete batchClose.GetRequest(i);
        }
        delete batchClose;
    }
}
